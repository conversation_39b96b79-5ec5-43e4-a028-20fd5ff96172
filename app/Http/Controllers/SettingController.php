<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Header;

class SettingController extends Controller
{
    public function index()
    {
        $headers = Header::all();
        return Inertia::render('setting',[
            'headers' => $headers,
        ]);
    }

    public function addHeader(Request $request)
    {
        Header::create([
            'header_images' => $request->file('image')->store('header', 'public'),
            'header_deskripsi' => $request->input('deskripsi')
        ]);
        return redirect()->back()->with('success', 'Header berhasil ditambahkan!');
    }

}
